<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Tests\Feature\CalendarModalReservationTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" tests="8" assertions="39" errors="0" failures="3" skipped="0" time="0.143138">
    <testcase name="calendar_page_loads_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="37" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="3" time="0.083353"/>
    <testcase name="calendar_modal_redirects_to_reservation_form_with_data" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="47" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="6" time="0.014207"/>
    <testcase name="ajax_reservation_creation_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="65" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="10" time="0.009181"/>
    <testcase name="ajax_reservation_returns_validation_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="97" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="4" time="0.006952"/>
    <testcase name="ajax_reservation_handles_availability_conflicts" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="115" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="4" time="0.005482"/>
    <testcase name="reservation_form_pre_populates_from_url_parameters" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="143" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="2" time="0.015076">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Feature\CalendarModalReservationTest::reservation_form_pre_populates_from_url_parameters
Failed asserting that '&lt;!DOCTYPE html&gt;\n
&lt;html lang="en" dir="ltr" data-nav-layout="vertical"\n
    data-theme-mode="light" data-header-styles="light" data-menu-styles="dark" data-toggled="close"&gt;\n
\n
&lt;head&gt;\n
    &lt;!-- Meta Data --&gt;\n
    &lt;meta charset="UTF-8"&gt;\n
    &lt;meta name='viewport' content='width=device-width, initial-scale=1.0'&gt;\n
    &lt;meta http-equiv="X-UA-Compatible" content="IE=edge"&gt;\n
    &lt;title&gt;New Reservation - FPMP Sport Park&lt;/title&gt;\n
    &lt;meta name="Description" content="Field Management and Booking System"&gt;\n
    &lt;meta name="Author" content="Field Management System"&gt;\n
    &lt;meta name="keywords" content="field management,booking system,admin dashboard,laravel"&gt;\n
    &lt;meta name="csrf-token" content="cupW1gMYFD3BrcENHG2vE8GdxTJO0qrU3YUQ0N9w"&gt;\n
\n
    &lt;!-- Favicon --&gt;\n
    &lt;link rel="icon" href="http://localhost/assets/images/brand-logos/favicon.ico" type="image/x-icon"&gt;\n
\n
    &lt;!-- Choices JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/choices.js/public/assets/scripts/choices.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Main Theme Js --&gt;\n
    &lt;script src="http://localhost/assets/js/main.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Bootstrap Css --&gt;\n
    &lt;link id="style" href="http://localhost/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet"&gt;\n
\n
    &lt;!-- Style Css --&gt;\n
    &lt;link href="http://localhost/assets/css/styles.min.css" rel="stylesheet"&gt;\n
\n
    &lt;!-- Icons Css --&gt;\n
    &lt;link href="http://localhost/assets/css/icons.css" rel="stylesheet"&gt;\n
\n
    &lt;!-- Node Waves Css --&gt;\n
    &lt;link href="http://localhost/assets/libs/node-waves/waves.min.css" rel="stylesheet"&gt;\n
\n
    &lt;!-- Simplebar Css --&gt;\n
    &lt;link href="http://localhost/assets/libs/simplebar/simplebar.min.css" rel="stylesheet"&gt;\n
\n
    &lt;!-- Color Picker Css --&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/libs/flatpickr/flatpickr.min.css"&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/libs/@simonwep/pickr/themes/nano.min.css"&gt;\n
\n
    &lt;!-- Choices Css --&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/libs/choices.js/public/assets/styles/choices.min.css"&gt;\n
\n
    &lt;!-- FullCalendar CSS --&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/libs/fullcalendar/main.min.css"&gt;\n
\n
    &lt;!-- DataTables CSS --&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css"&gt;\n
\n
    &lt;!-- Custom Pagination CSS --&gt;\n
    &lt;link rel="stylesheet" href="http://localhost/assets/css/custom-pagination.css"&gt;\n
\n
    &lt;/head&gt;\n
\n
&lt;body&gt;\n
\n
    &lt;!-- Start Switcher --&gt;\n
    &lt;div class="offcanvas offcanvas-end" tabindex="-1" id="switcher-canvas" aria-labelledby="offcanvasRightLabel"&gt;\n
        &lt;div class="offcanvas-header border-bottom"&gt;\n
            &lt;h5 class="offcanvas-title text-default" id="offcanvasRightLabel"&gt;Switcher&lt;/h5&gt;\n
            &lt;button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"&gt;&lt;/button&gt;\n
        &lt;/div&gt;\n
        &lt;div class="offcanvas-body"&gt;\n
            &lt;nav class="border-bottom border-block-end-dashed"&gt;\n
                &lt;div class="nav nav-tabs nav-justified" id="switcher-main-tab" role="tablist"&gt;\n
                    &lt;button class="nav-link active" id="switcher-home-tab" data-bs-toggle="tab"\n
                        data-bs-target="#switcher-home" type="button" role="tab" aria-controls="switcher-home"\n
                        aria-selected="true"&gt;Theme Styles&lt;/button&gt;\n
                    &lt;button class="nav-link" id="switcher-profile-tab" data-bs-toggle="tab"\n
                        data-bs-target="#switcher-profile" type="button" role="tab"\n
                        aria-controls="switcher-profile" aria-selected="false"&gt;Theme Colors&lt;/button&gt;\n
                &lt;/div&gt;\n
            &lt;/nav&gt;\n
            &lt;div class="tab-content" id="nav-tabContent"&gt;\n
                &lt;div class="tab-pane fade show active border-0" id="switcher-home" role="tabpanel"\n
                    aria-labelledby="switcher-home-tab" tabindex="0"&gt;\n
                    &lt;div class=""&gt;\n
                        &lt;p class="switcher-style-head"&gt;Theme Color Mode:&lt;/p&gt;\n
                        &lt;div class="row switcher-style gx-0"&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-light-theme"&gt;\n
                                        Light\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="theme-style"\n
                                        id="switcher-light-theme" checked&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-dark-theme"&gt;\n
                                        Dark\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="theme-style"\n
                                        id="switcher-dark-theme"&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                        &lt;/div&gt;\n
                    &lt;/div&gt;\n
                    &lt;div class=""&gt;\n
                        &lt;p class="switcher-style-head"&gt;Directions:&lt;/p&gt;\n
                        &lt;div class="row switcher-style gx-0"&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-ltr"&gt;\n
                                        LTR\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="direction"\n
                                        id="switcher-ltr" checked&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-rtl"&gt;\n
                                        RTL\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="direction"\n
                                        id="switcher-rtl"&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                        &lt;/div&gt;\n
                    &lt;/div&gt;\n
                    &lt;div class=""&gt;\n
                        &lt;p class="switcher-style-head"&gt;Navigation Styles:&lt;/p&gt;\n
                        &lt;div class="row switcher-style gx-0"&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-vertical"&gt;\n
                                        Vertical\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="navigation-style"\n
                                        id="switcher-vertical" checked&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                            &lt;div class="col-4"&gt;\n
                                &lt;div class="form-check switch-select"&gt;\n
                                    &lt;label class="form-check-label" for="switcher-horizontal"&gt;\n
                                        Horizontal\n
                                    &lt;/label&gt;\n
                                    &lt;input class="form-check-input" type="radio" name="navigation-style"\n
                                        id="switcher-horizontal"&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                        &lt;/div&gt;\n
                    &lt;/div&gt;\n
                &lt;/div&gt;\n
            &lt;/div&gt;\n
        &lt;/div&gt;\n
    &lt;/div&gt;\n
    &lt;!-- End Switcher --&gt;\n
\n
    &lt;!-- Loader --&gt;\n
    &lt;div id="loader"&gt;\n
        &lt;img src="http://localhost/assets/images/media/loader.svg" alt=""&gt;\n
    &lt;/div&gt;\n
    &lt;!-- Loader --&gt;\n
\n
    &lt;div class="page"&gt;\n
        &lt;!-- app-header --&gt;\n
        &lt;header class="app-header"&gt;\n
\n
    &lt;!-- Start::main-header-container --&gt;\n
    &lt;div class="main-header-container container-fluid"&gt;\n
\n
        &lt;!-- Start::header-content-left --&gt;\n
        &lt;div class="header-content-left"&gt;\n
\n
            &lt;!-- Start::header-element --&gt;\n
            &lt;div class="header-element"&gt;\n
                &lt;div class="horizontal-logo"&gt;\n
                    &lt;a href="http://localhost/dashboard" class="header-logo"&gt;\n
                        &lt;img src="http://localhost/assets/images/brand-logos/desktop-logo.png" alt="logo"\n
                            class="desktop-logo"&gt;\n
                        &lt;img src="http://localhost/assets/images/brand-logos/toggle-logo.png" alt="logo"\n
                            class="toggle-logo"&gt;\n
                        &lt;img src="http://localhost/assets/images/brand-logos/desktop-dark.png" alt="logo"\n
                            class="desktop-dark"&gt;\n
                        &lt;img src="http://localhost/assets/images/brand-logos/toggle-dark.png" alt="logo"\n
                            class="toggle-dark"&gt;\n
                    &lt;/a&gt;\n
                &lt;/div&gt;\n
            &lt;/div&gt;\n
            &lt;!-- End::header-element --&gt;\n
\n
            &lt;!-- Start::header-element --&gt;\n
            &lt;div class="header-element"&gt;\n
                &lt;!-- Start::header-link --&gt;\n
                &lt;a aria-label="Hide Sidebar"\n
                    class="sidemenu-toggle header-link animated-arrow hor-toggle horizontal-navtoggle"\n
                    data-bs-toggle="sidebar" href="javascript:void(0);"&gt;&lt;span&gt;&lt;/span&gt;&lt;/a&gt;\n
                &lt;!-- End::header-link --&gt;\n
            &lt;/div&gt;\n
            &lt;!-- End::header-element --&gt;\n
\n
        &lt;/div&gt;\n
        &lt;!-- End::header-content-left --&gt;\n
\n
        &lt;!-- Start::header-content-right --&gt;\n
        &lt;div class="header-content-right"&gt;\n
\n
\n
\n
            &lt;!-- Start::header-element --&gt;\n
            &lt;div class="header-element"&gt;\n
                &lt;!-- Start::header-link|dropdown-toggle --&gt;\n
                &lt;a href="#" class="header-link dropdown-toggle" id="mainHeaderProfile" data-bs-toggle="dropdown"\n
                    data-bs-auto-close="outside" aria-expanded="false"&gt;\n
                    &lt;div class="d-flex align-items-center"&gt;\n
                        &lt;div class="me-sm-2 me-0"&gt;\n
                            &lt;img src="http://localhost/assets/images/faces/9.jpg" alt="img" width="32"\n
                                height="32" class="rounded-circle"&gt;\n
                        &lt;/div&gt;\n
                        &lt;div class="d-sm-block d-none"&gt;\n
                            &lt;p class="fw-semibold mb-0 lh-1"&gt;Prof. Jose Conroy DDS&lt;/p&gt;\n
                            &lt;span\n
                                class="op-7 fw-normal d-block fs-11"&gt;Employee&lt;/span&gt;\n
                        &lt;/div&gt;\n
                    &lt;/div&gt;\n
                &lt;/a&gt;\n
                &lt;!-- End::header-link|dropdown-toggle --&gt;\n
                &lt;ul class="main-header-dropdown dropdown-menu pt-0 overflow-hidden header-profile-dropdown dropdown-menu-end"\n
                    aria-labelledby="mainHeaderProfile"&gt;\n
                    &lt;li&gt;&lt;a class="dropdown-item d-flex" href="http://localhost/profile"&gt;&lt;i\n
                                class="ti ti-user-circle fs-18 me-2 op-7"&gt;&lt;/i&gt;Profile&lt;/a&gt;&lt;/li&gt;\n
\n
                                            &lt;li&gt;&lt;a class="dropdown-item d-flex" href="http://localhost/reservations"&gt;&lt;i\n
                                    class="ti ti-calendar-event fs-18 me-2 op-7"&gt;&lt;/i&gt;My Reservations&lt;/a&gt;&lt;/li&gt;\n
                        &lt;li&gt;&lt;a class="dropdown-item d-flex" href="http://localhost/reservations/create"&gt;&lt;i\n
                                    class="ti ti-plus fs-18 me-2 op-7"&gt;&lt;/i&gt;New Reservation&lt;/a&gt;&lt;/li&gt;\n
                        &lt;li&gt;&lt;a class="dropdown-item d-flex" href="http://localhost/employee/features"&gt;&lt;i\n
                                    class="ti ti-inbox fs-18 me-2 op-7"&gt;&lt;/i&gt;Features&lt;/a&gt;&lt;/li&gt;\n
                    \n
                    \n
                    &lt;li&gt;&lt;a class="dropdown-item d-flex border-block-end" href="javascript:void(0);"&gt;&lt;i\n
                                class="ti ti-adjustments-horizontal fs-18 me-2 op-7"&gt;&lt;/i&gt;Settings&lt;/a&gt;&lt;/li&gt;\n
                    &lt;li&gt;\n
                        &lt;form method="POST" action="http://localhost/logout"&gt;\n
                            &lt;input type="hidden" name="_token" value="cupW1gMYFD3BrcENHG2vE8GdxTJO0qrU3YUQ0N9w" autocomplete="off"&gt;                            &lt;a class="dropdown-item d-flex" href="http://localhost/logout"\n
                                onclick="event.preventDefault(); this.closest('form').submit();"&gt;\n
                                &lt;i class="ti ti-logout fs-18 me-2 op-7"&gt;&lt;/i&gt;Log Out\n
                            &lt;/a&gt;\n
                        &lt;/form&gt;\n
                    &lt;/li&gt;\n
                &lt;/ul&gt;\n
            &lt;/div&gt;\n
            &lt;!-- End::header-element --&gt;\n
\n
            &lt;!-- Start::header-element --&gt;\n
            &lt;div class="header-element"&gt;\n
                &lt;!-- Start::header-link|switcher-icon --&gt;\n
                &lt;a href="#" class="header-link switcher-icon" data-bs-toggle="offcanvas"\n
                    data-bs-target="#switcher-canvas"&gt;\n
                    &lt;i class="bx bx-cog header-link-icon"&gt;&lt;/i&gt;\n
                &lt;/a&gt;\n
                &lt;!-- End::header-link|switcher-icon --&gt;\n
            &lt;/div&gt;\n
            &lt;!-- End::header-element --&gt;\n
\n
        &lt;/div&gt;\n
        &lt;!-- End::header-content-right --&gt;\n
\n
    &lt;/div&gt;\n
    &lt;!-- End::main-header-container --&gt;\n
\n
&lt;/header&gt;\n
        &lt;!-- /app-header --&gt;\n
\n
        &lt;!-- Start::app-sidebar --&gt;\n
        &lt;aside class="app-sidebar sticky" id="sidebar"&gt;\n
\n
    &lt;!-- Start::main-sidebar-header --&gt;\n
    &lt;div class="main-sidebar-header"&gt;\n
        &lt;a href="http://localhost/dashboard" class="header-logo"&gt;\n
            &lt;img src="http://localhost/assets/images/brand-logos/desktop-logo.png" alt="logo" class="desktop-logo"&gt;\n
            &lt;img src="http://localhost/assets/images/brand-logos/toggle-logo.png" alt="logo" class="toggle-logo"&gt;\n
            &lt;img src="http://localhost/assets/images/brand-logos/desktop-dark.png" alt="logo" class="desktop-dark"&gt;\n
            &lt;img src="http://localhost/assets/images/brand-logos/toggle-dark.png" alt="logo" class="toggle-dark"&gt;\n
        &lt;/a&gt;\n
    &lt;/div&gt;\n
    &lt;!-- End::main-sidebar-header --&gt;\n
\n
    &lt;!-- Start::main-sidebar --&gt;\n
    &lt;div class="main-sidebar" id="sidebar-scroll"&gt;\n
\n
        &lt;!-- Start::nav --&gt;\n
        &lt;nav class="main-menu-container nav nav-pills flex-column sub-open"&gt;\n
            &lt;div class="slide-left" id="slide-left"&gt;\n
                &lt;svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24"\n
                    viewBox="0 0 24 24"&gt;\n
                    &lt;path d="M13.293 6.293 7.586 12l5.707 5.707 1.414-1.414L10.414 12l4.293-4.293z"&gt;&lt;/path&gt;\n
                &lt;/svg&gt;\n
            &lt;/div&gt;\n
            &lt;ul class="main-menu"&gt;\n
                &lt;!-- Start::slide__category --&gt;\n
                &lt;li class="slide__category"&gt;&lt;span class="category-name"&gt;Main&lt;/span&gt;&lt;/li&gt;\n
                &lt;!-- End::slide__category --&gt;\n
\n
                &lt;!-- Start::slide --&gt;\n
                &lt;li class="slide"&gt;\n
                    &lt;a href="http://localhost/dashboard"\n
                        class="side-menu__item "&gt;\n
                        &lt;i class="bx bx-home side-menu__icon"&gt;&lt;/i&gt;\n
                        &lt;span class="side-menu__label"&gt;Dashboard&lt;/span&gt;\n
                    &lt;/a&gt;\n
                &lt;/li&gt;\n
                &lt;!-- End::slide --&gt;\n
\n
                                    &lt;!-- Start::slide --&gt;\n
                    &lt;li class="slide"&gt;\n
                        &lt;a href="http://localhost/employee/features"\n
                            class="side-menu__item "&gt;\n
                            &lt;i class="bx bx-star side-menu__icon"&gt;&lt;/i&gt;\n
                            &lt;span class="side-menu__label"&gt;Features&lt;/span&gt;\n
                        &lt;/a&gt;\n
                    &lt;/li&gt;\n
                    &lt;!-- End::slide --&gt;\n
                \n
                &lt;!-- Start::slide__category --&gt;\n
                &lt;li class="slide__category"&gt;&lt;span class="category-name"&gt;Booking Management&lt;/span&gt;&lt;/li&gt;\n
                &lt;!-- End::slide__category --&gt;\n
\n
                &lt;!-- Start::slide --&gt;\n
                &lt;li class="slide"&gt;\n
                    &lt;a href="http://localhost/calendar"\n
                        class="side-menu__item "&gt;\n
                        &lt;i class="bx bx-calendar side-menu__icon"&gt;&lt;/i&gt;\n
                        &lt;span class="side-menu__label"&gt;Calendar&lt;/span&gt;\n
                    &lt;/a&gt;\n
                &lt;/li&gt;\n
                &lt;!-- End::slide --&gt;\n
\n
                                    &lt;!-- Start::slide --&gt;\n
                    &lt;li class="slide"&gt;\n
                        &lt;a href="http://localhost/reservations/create"\n
                            class="side-menu__item active"&gt;\n
                            &lt;i class="bx bx-plus-circle side-menu__icon"&gt;&lt;/i&gt;\n
                            &lt;span class="side-menu__label"&gt;Quick Reserve&lt;/span&gt;\n
                            &lt;span class="badge bg-primary ms-auto"&gt;FPMP&lt;/span&gt;\n
                        &lt;/a&gt;\n
                    &lt;/li&gt;\n
                    &lt;!-- End::slide --&gt;\n
\n
                    &lt;!-- Start::slide --&gt;\n
                    &lt;li class="slide has-sub open"&gt;\n
                        &lt;a href="javascript:void(0);" class="side-menu__item"&gt;\n
                            &lt;i class="bx bx-calendar-event side-menu__icon"&gt;&lt;/i&gt;\n
                            &lt;span class="side-menu__label"&gt;FPMP Reservations&lt;/span&gt;\n
                            &lt;i class="fe fe-chevron-right side-menu__angle"&gt;&lt;/i&gt;\n
                        &lt;/a&gt;\n
                        &lt;ul class="slide-menu child1"&gt;\n
                            &lt;li class="slide side-menu__label1"&gt;\n
                                &lt;a href="javascript:void(0)"&gt;FPMP Reservations&lt;/a&gt;\n
                            &lt;/li&gt;\n
                            &lt;li class="slide"&gt;\n
                                &lt;a href="http://localhost/reservations"\n
                                    class="side-menu__item "&gt;My\n
                                    Reservations&lt;/a&gt;\n
                            &lt;/li&gt;\n
                            &lt;li class="slide"&gt;\n
                                &lt;a href="http://localhost/reservations/create"\n
                                    class="side-menu__item active"&gt;New\n
                                    Reservation&lt;/a&gt;\n
                            &lt;/li&gt;\n
                        &lt;/ul&gt;\n
                    &lt;/li&gt;\n
                    &lt;!-- End::slide --&gt;\n
                \n
                &lt;!-- Start::slide --&gt;\n
                &lt;li class="slide has-sub "&gt;\n
                    &lt;a href="javascript:void(0);" class="side-menu__item"&gt;\n
                        &lt;i class="bx bx-book-bookmark side-menu__icon"&gt;&lt;/i&gt;\n
                        &lt;span class="side-menu__label"&gt;Admin Bookings&lt;/span&gt;\n
                        &lt;i class="fe fe-chevron-right side-menu__angle"&gt;&lt;/i&gt;\n
                    &lt;/a&gt;\n
                    &lt;ul class="slide-menu child1"&gt;\n
                        &lt;li class="slide side-menu__label1"&gt;\n
                            &lt;a href="javascript:void(0)"&gt;Admin Bookings&lt;/a&gt;\n
                        &lt;/li&gt;\n
                        &lt;li class="slide"&gt;\n
                            &lt;a href="http://localhost/bookings"\n
                                class="side-menu__item "&gt;All\n
                                Bookings&lt;/a&gt;\n
                        &lt;/li&gt;\n
                        &lt;li class="slide"&gt;\n
                            &lt;a href="http://localhost/bookings/create"\n
                                class="side-menu__item "&gt;Create\n
                                Booking&lt;/a&gt;\n
                        &lt;/li&gt;\n
                    &lt;/ul&gt;\n
                &lt;/li&gt;\n
                &lt;!-- End::slide --&gt;\n
\n
                \n
                &lt;!-- Start::slide__category --&gt;\n
                &lt;li class="slide__category"&gt;&lt;span class="category-name"&gt;Account&lt;/span&gt;&lt;/li&gt;\n
                &lt;!-- End::slide__category --&gt;\n
\n
                &lt;!-- Start::slide --&gt;\n
                &lt;li class="slide"&gt;\n
                    &lt;a href="http://localhost/profile"\n
                        class="side-menu__item "&gt;\n
                        &lt;i class="bx bx-user-circle side-menu__icon"&gt;&lt;/i&gt;\n
                        &lt;span class="side-menu__label"&gt;Profile&lt;/span&gt;\n
                    &lt;/a&gt;\n
                &lt;/li&gt;\n
                &lt;!-- End::slide --&gt;\n
\n
                &lt;!-- Start::slide --&gt;\n
                &lt;li class="slide"&gt;\n
                    &lt;form method="POST" action="http://localhost/logout"&gt;\n
                        &lt;input type="hidden" name="_token" value="cupW1gMYFD3BrcENHG2vE8GdxTJO0qrU3YUQ0N9w" autocomplete="off"&gt;                        &lt;a href="http://localhost/logout" class="side-menu__item"\n
                            onclick="event.preventDefault(); this.closest('form').submit();"&gt;\n
                            &lt;i class="bx bx-log-out side-menu__icon"&gt;&lt;/i&gt;\n
                            &lt;span class="side-menu__label"&gt;Logout&lt;/span&gt;\n
                        &lt;/a&gt;\n
                    &lt;/form&gt;\n
                &lt;/li&gt;\n
                &lt;!-- End::slide --&gt;\n
\n
            &lt;/ul&gt;\n
            &lt;div class="slide-right" id="slide-right"&gt;&lt;svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191"\n
                    width="24" height="24" viewBox="0 0 24 24"&gt;\n
                    &lt;path d="M10.707 17.707 16.414 12l-5.707-5.707-1.414 1.414L13.586 12l-4.293 4.293z"&gt;&lt;/path&gt;\n
                &lt;/svg&gt;&lt;/div&gt;\n
        &lt;/nav&gt;\n
        &lt;!-- End::nav --&gt;\n
\n
    &lt;/div&gt;\n
    &lt;!-- End::main-sidebar --&gt;\n
&lt;/aside&gt;\n
        &lt;!-- End::app-sidebar --&gt;\n
\n
        &lt;!-- Start::app-content --&gt;\n
        &lt;div class="main-content app-content"&gt;\n
            &lt;div class="container-fluid"&gt;\n
                    &lt;!-- Page Header --&gt;\n
    &lt;div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb"&gt;\n
        &lt;h1 class="page-title fw-semibold fs-18 mb-0"&gt;New Reservation&lt;/h1&gt;\n
        &lt;div class="ms-md-1 ms-0"&gt;\n
            &lt;nav&gt;\n
                &lt;ol class="breadcrumb mb-0"&gt;\n
                    &lt;li class="breadcrumb-item"&gt;&lt;a href="http://localhost/dashboard"&gt;Home&lt;/a&gt;&lt;/li&gt;\n
                    &lt;li class="breadcrumb-item"&gt;&lt;a href="http://localhost/reservations"&gt;Reservations&lt;/a&gt;&lt;/li&gt;\n
                    &lt;li class="breadcrumb-item active" aria-current="page"&gt;New&lt;/li&gt;\n
                &lt;/ol&gt;\n
            &lt;/nav&gt;\n
        &lt;/div&gt;\n
    &lt;/div&gt;\n
    &lt;!-- Page Header Close --&gt;\n
\n
    &lt;!-- Success/Error Messages --&gt;\n
    \n
    \n
    &lt;!-- Reservation Form --&gt;\n
    &lt;div class="row"&gt;\n
        &lt;div class="col-xl-12"&gt;\n
            &lt;div class="card custom-card"&gt;\n
                &lt;div class="card-header justify-content-between"&gt;\n
                    &lt;div class="card-title"&gt;\n
                        &lt;i class="ti ti-calendar-plus me-2"&gt;&lt;/i&gt;Create New Reservation\n
                    &lt;/div&gt;\n
                    &lt;div class="d-flex gap-2"&gt;\n
                        &lt;a href="http://localhost/reservations" class="btn btn-secondary btn-sm"&gt;\n
                            &lt;i class="ti ti-arrow-left me-1"&gt;&lt;/i&gt;Back to Reservations\n
                        &lt;/a&gt;\n
                    &lt;/div&gt;\n
                &lt;/div&gt;\n
                &lt;div class="card-body"&gt;\n
                    &lt;form method="POST" action="http://localhost/reservations" id="reservationForm"&gt;\n
                        &lt;input type="hidden" name="_token" value="cupW1gMYFD3BrcENHG2vE8GdxTJO0qrU3YUQ0N9w" autocomplete="off"&gt;\n
                        &lt;div class="row gy-4"&gt;\n
                            &lt;!-- Field Selection --&gt;\n
                            &lt;div class="col-xl-6"&gt;\n
                                &lt;div class="card custom-card shadow-none border"&gt;\n
                                    &lt;div class="card-header"&gt;\n
                                        &lt;div class="card-title"&gt;Field Selection&lt;/div&gt;\n
                                    &lt;/div&gt;\n
                                    &lt;div class="card-body"&gt;\n
                                        &lt;div class="row gy-3"&gt;\n
                                            &lt;!-- Field Selection --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="field_id" class="form-label"&gt;Field &lt;span\n
                                                        class="text-danger"&gt;*&lt;/span&gt;&lt;/label&gt;\n
                                                &lt;select name="field_id" id="field_id" required\n
                                                    onchange="updateFieldInfo(); loadAvailability();"\n
                                                    class="form-select "&gt;\n
                                                    &lt;option value=""&gt;Select Field&lt;/option&gt;\n
                                                                                                            &lt;option value="1"\n
                                                            data-rate="75.00"\n
                                                            data-capacity="68"\n
                                                            data-type="Soccer"\n
                                                            data-opening="08:00"\n
                                                            data-closing="22:00"\n
                                                            data-min-hours="1"\n
                                                            data-max-hours="8"\n
                                                            selected&gt;\n
                                                            FPMP Test Soccer Field -\n
                                                            $75.00/hr\n
                                                        &lt;/option&gt;\n
                                                                                                    &lt;/select&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Field Information Display --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;div id="fieldInfo"\n
                                                    class="alert alert-info "&gt;\n
                                                    &lt;h6 class="fw-semibold"&gt;Field Information&lt;/h6&gt;\n
                                                    &lt;p class="mb-1"&gt;&lt;strong&gt;Type:&lt;/strong&gt; &lt;span\n
                                                            id="fieldType"&gt;Soccer&lt;/span&gt;&lt;/p&gt;\n
                                                    &lt;p class="mb-1"&gt;&lt;strong&gt;Capacity:&lt;/strong&gt; &lt;span\n
                                                            id="fieldCapacity"&gt;68&lt;/span&gt;\n
                                                        people&lt;/p&gt;\n
                                                    &lt;p class="mb-1"&gt;&lt;strong&gt;Hourly Rate:&lt;/strong&gt; $&lt;span\n
                                                            id="fieldRate"&gt;75.00&lt;/span&gt;\n
                                                    &lt;/p&gt;\n
                                                    &lt;p class="mb-1"&gt;&lt;strong&gt;Working Hours:&lt;/strong&gt; &lt;span\n
                                                            id="fieldHours"&gt;08:00 -\n
                                                            22:00&lt;/span&gt;&lt;/p&gt;\n
                                                    &lt;p class="mb-0"&gt;&lt;strong&gt;Booking Duration:&lt;/strong&gt; &lt;span\n
                                                            id="fieldDuration"&gt;1 -\n
                                                            8&lt;/span&gt; hours&lt;/p&gt;\n
                                                &lt;/div&gt;\n
                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Date Selection --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="booking_date" class="form-label"&gt;Date &lt;span\n
                                                        class="text-danger"&gt;*&lt;/span&gt;&lt;/label&gt;\n
                                                &lt;input type="date" name="booking_date" id="booking_date"\n
                                                    value="2024-06-25"\n
                                                    min="2025-07-06" required onchange="loadAvailability();"\n
                                                    class="form-control "&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Time Selection --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="start_time" class="form-label"&gt;Start Time &lt;span\n
                                                        class="text-danger"&gt;*&lt;/span&gt;&lt;/label&gt;\n
                                                &lt;select name="start_time" id="start_time" required\n
                                                    onchange="calculateCost();"\n
                                                    class="form-select "&gt;\n
                                                    &lt;option value=""&gt;Select Time&lt;/option&gt;\n
                                                    &lt;!-- Options will be populated by JavaScript --&gt;\n
                                                &lt;/select&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Duration --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="duration_hours" class="form-label"&gt;Duration &lt;span\n
                                                        class="text-danger"&gt;*&lt;/span&gt;&lt;/label&gt;\n
                                                &lt;select name="duration_hours" id="duration_hours" required\n
                                                    onchange="calculateCost(); loadAvailability();"\n
                                                    class="form-select "&gt;\n
                                                                                                            &lt;option value="1"\n
                                                            &gt;\n
                                                            1 hour\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="2"\n
                                                            &gt;\n
                                                            2 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="3"\n
                                                            selected&gt;\n
                                                            3 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="4"\n
                                                            &gt;\n
                                                            4 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="5"\n
                                                            &gt;\n
                                                            5 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="6"\n
                                                            &gt;\n
                                                            6 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="7"\n
                                                            &gt;\n
                                                            7 hours\n
                                                        &lt;/option&gt;\n
                                                                                                            &lt;option value="8"\n
                                                            &gt;\n
                                                            8 hours\n
                                                        &lt;/option&gt;\n
                                                                                                    &lt;/select&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Availability Check --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;div id="availabilityCheck" class="d-none"&gt;\n
                                                    &lt;div id="availabilityMessage" class="alert"&gt;&lt;/div&gt;\n
                                                &lt;/div&gt;\n
                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Cost Display --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;div id="costDisplay" class="alert alert-success d-none"&gt;\n
                                                    &lt;h6 class="fw-semibold"&gt;Reservation Cost&lt;/h6&gt;\n
                                                    &lt;p class="mb-1"&gt;&lt;strong&gt;Total Cost: $&lt;span\n
                                                                id="totalCost"&gt;0.00&lt;/span&gt;&lt;/strong&gt;&lt;/p&gt;\n
                                                    &lt;p class="mb-0 fs-12"&gt;Rate: $&lt;span id="displayRate"&gt;0.00&lt;/span&gt;/hour ×\n
                                                        &lt;span id="displayDuration"&gt;1&lt;/span&gt; hour(s)\n
                                                    &lt;/p&gt;\n
                                                &lt;/div&gt;\n
                                            &lt;/div&gt;\n
                                        &lt;/div&gt;\n
                                    &lt;/div&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
\n
                            &lt;!-- Customer Information --&gt;\n
                            &lt;div class="col-xl-6"&gt;\n
                                &lt;div class="card custom-card shadow-none border"&gt;\n
                                    &lt;div class="card-header"&gt;\n
                                        &lt;div class="card-title"&gt;Customer Information&lt;/div&gt;\n
                                    &lt;/div&gt;\n
                                    &lt;div class="card-body"&gt;\n
                                        &lt;div class="alert alert-info"&gt;\n
                                            &lt;strong&gt;Note:&lt;/strong&gt; Leave customer fields empty to use your account\n
                                            information.\n
                                        &lt;/div&gt;\n
\n
                                        &lt;div class="row gy-3"&gt;\n
                                            &lt;!-- Customer Name --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="customer_name" class="form-label"&gt;Customer Name&lt;/label&gt;\n
                                                &lt;input type="text" name="customer_name" id="customer_name"\n
                                                    value=""\n
                                                    placeholder="Leave empty to use your name (Prof. Jose Conroy DDS)"\n
                                                    class="form-control "&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Customer Email --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="customer_email" class="form-label"&gt;Customer Email&lt;/label&gt;\n
                                                &lt;input type="email" name="customer_email" id="customer_email"\n
                                                    value=""\n
                                                    placeholder="Leave empty to use your email (<EMAIL>)"\n
                                                    class="form-control "&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Customer Phone --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="customer_phone" class="form-label"&gt;Customer Phone&lt;/label&gt;\n
                                                &lt;input type="tel" name="customer_phone" id="customer_phone"\n
                                                    value=""\n
                                                    placeholder="Phone number (optional)"\n
                                                    class="form-control "&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Special Requests --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;label for="special_requests" class="form-label"&gt;Special Requests&lt;/label&gt;\n
                                                &lt;textarea name="special_requests" id="special_requests" rows="3"\n
                                                    class="form-control "\n
                                                    placeholder="Any special requirements or notes..."&gt;&lt;/textarea&gt;\n
                                                                                            &lt;/div&gt;\n
\n
                                            &lt;!-- Reservation Rules --&gt;\n
                                            &lt;div class="col-xl-12"&gt;\n
                                                &lt;div class="alert alert-warning"&gt;\n
                                                    &lt;h6 class="fw-semibold"&gt;FPMP Reservation Rules&lt;/h6&gt;\n
                                                    &lt;ul class="mb-0 fs-12"&gt;\n
                                                        &lt;li&gt;• Reservations are available from 8:00 AM to 10:00 PM&lt;/li&gt;\n
                                                        &lt;li&gt;• All reservations are automatically confirmed&lt;/li&gt;\n
                                                        &lt;li&gt;• Cancellations must be made at least 24 hours in advance&lt;/li&gt;\n
                                                        &lt;li&gt;• Modifications must be made at least 24 hours in advance&lt;/li&gt;\n
                                                        &lt;li&gt;• Please arrive 15 minutes before your scheduled time&lt;/li&gt;\n
                                                    &lt;/ul&gt;\n
                                                &lt;/div&gt;\n
                                            &lt;/div&gt;\n
                                        &lt;/div&gt;\n
                                    &lt;/div&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                        &lt;/div&gt;\n
\n
                        &lt;!-- Form Actions --&gt;\n
                        &lt;div class="row"&gt;\n
                            &lt;div class="col-xl-12"&gt;\n
                                &lt;div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top"&gt;\n
                                    &lt;a href="http://localhost/reservations" class="btn btn-secondary"&gt;\n
                                        &lt;i class="ti ti-x me-1"&gt;&lt;/i&gt;Cancel\n
                                    &lt;/a&gt;\n
                                    &lt;button type="submit" id="submitBtn" class="btn btn-primary"&gt;\n
                                        &lt;i class="ti ti-check me-1"&gt;&lt;/i&gt;Create Reservation\n
                                    &lt;/button&gt;\n
                                &lt;/div&gt;\n
                            &lt;/div&gt;\n
                        &lt;/div&gt;\n
                    &lt;/form&gt;\n
                &lt;/div&gt;\n
            &lt;/div&gt;\n
        &lt;/div&gt;\n
    &lt;/div&gt;\n
            &lt;/div&gt;\n
        &lt;/div&gt;\n
        &lt;!-- End::app-content --&gt;\n
\n
        &lt;!-- Footer Start --&gt;\n
        &lt;footer class="footer mt-auto py-3 bg-white text-center"&gt;\n
    &lt;div class="container"&gt;\n
        &lt;span class="text-muted"&gt;Copyright © &lt;span id="year"&gt;&lt;/span&gt; &lt;a\n
                href="javascript:void(0);" class="text-dark fw-semibold"&gt;Field Management System&lt;/a&gt;.\n
            Designed with &lt;span class="bi bi-heart-fill text-danger"&gt;&lt;/span&gt; by &lt;a href="javascript:void(0);"&gt;\n
                &lt;span class="fw-semibold text-primary text-decoration-underline"&gt;Admin Team&lt;/span&gt;\n
            &lt;/a&gt; All\n
            rights\n
            reserved\n
        &lt;/span&gt;\n
    &lt;/div&gt;\n
&lt;/footer&gt;\n
\n
&lt;script&gt;\n
    document.getElementById('year').textContent = new Date().getFullYear();\n
&lt;/script&gt;\n
        &lt;!-- Footer End --&gt;\n
    &lt;/div&gt;\n
\n
    &lt;!-- Scroll To Top --&gt;\n
    &lt;div class="scrollToTop"&gt;\n
        &lt;span class="arrow"&gt;&lt;i class="ri-arrow-up-s-fill fs-20"&gt;&lt;/i&gt;&lt;/span&gt;\n
    &lt;/div&gt;\n
    &lt;div id="responsive-overlay"&gt;&lt;/div&gt;\n
    &lt;!-- Scroll To Top --&gt;\n
\n
    &lt;!-- Popper JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/@popperjs/core/umd/popper.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Bootstrap JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/bootstrap/js/bootstrap.bundle.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Defaultmenu JS --&gt;\n
    &lt;script src="http://localhost/assets/js/defaultmenu.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Node Waves JS--&gt;\n
    &lt;script src="http://localhost/assets/libs/node-waves/waves.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Sticky JS --&gt;\n
    &lt;script src="http://localhost/assets/js/sticky.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Simplebar JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/simplebar/simplebar.min.js"&gt;&lt;/script&gt;\n
    &lt;script src="http://localhost/assets/js/simplebar.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Color Picker JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/flatpickr/flatpickr.min.js"&gt;&lt;/script&gt;\n
    &lt;script src="http://localhost/assets/libs/@simonwep/pickr/pickr.es5.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Custom-Switcher JS --&gt;\n
    &lt;script src="http://localhost/assets/js/custom-switcher.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Choices JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/choices.js/public/assets/scripts/choices.min.js"&gt;&lt;/script&gt;\n
    &lt;script src="http://localhost/assets/js/choices.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- FullCalendar JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/fullcalendar/main.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- DataTables JS --&gt;\n
    &lt;script src="http://localhost/assets/libs/datatables.net/js/jquery.dataTables.min.js"&gt;&lt;/script&gt;\n
    &lt;script src="http://localhost/assets/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js"&gt;&lt;/script&gt;\n
\n
    &lt;!-- Custom JS --&gt;\n
    &lt;script src="http://localhost/assets/js/custom.js"&gt;&lt;/script&gt;\n
\n
        &lt;script&gt;\n
        function updateFieldInfo() {\n
            const fieldSelect = document.getElementById('field_id');\n
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];\n
            const fieldInfo = document.getElementById('fieldInfo');\n
\n
            if (selectedOption.value) {\n
                document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';\n
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';\n
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);\n
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (\n
                    selectedOption.dataset.closing || '22:00');\n
                document.getElementById('fieldDuration').textContent = (selectedOption.dataset.minHours || '1') + ' - ' + (\n
                    selectedOption.dataset.maxHours || '8') + ' hours';\n
                fieldInfo.classList.remove('d-none');\n
                updateDurationOptions();\n
                calculateCost();\n
            } else {\n
                fieldInfo.classList.add('d-none');\n
                document.getElementById('costDisplay').classList.add('d-none');\n
            }\n
        }\n
\n
        function updateDurationOptions() {\n
            const fieldSelect = document.getElementById('field_id');\n
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];\n
            const durationSelect = document.getElementById('duration_hours');\n
\n
            if (selectedOption.value) {\n
                const minHours = parseInt(selectedOption.dataset.minHours || 1);\n
                const maxHours = parseInt(selectedOption.dataset.maxHours || 8);\n
\n
                // Clear existing options\n
                durationSelect.innerHTML = '';\n
\n
                // Add new options based on field constraints\n
                for (let i = minHours; i &lt;= maxHours; i++) {\n
                    const option = document.createElement('option');\n
                    option.value = i;\n
                    option.textContent = i + ' ' + (i === 1 ? 'hour' : 'hours');\n
                    if (i === 1) option.selected = true;\n
                    durationSelect.appendChild(option);\n
                }\n
            }\n
        }\n
\n
        function calculateCost() {\n
            const fieldSelect = document.getElementById('field_id');\n
            const duration = document.getElementById('duration_hours').value;\n
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];\n
\n
            if (selectedOption.value &amp;&amp; duration) {\n
                const rate = parseFloat(selectedOption.dataset.rate || 0);\n
                const total = rate * parseInt(duration);\n
\n
                document.getElementById('totalCost').textContent = total.toFixed(2);\n
                document.getElementById('displayRate').textContent = rate.toFixed(2);\n
                document.getElementById('displayDuration').textContent = duration;\n
                document.getElementById('costDisplay').classList.remove('d-none');\n
            } else {\n
                document.getElementById('costDisplay').classList.add('d-none');\n
            }\n
        }\n
\n
        function loadAvailability() {\n
            const fieldId = document.getElementById('field_id').value;\n
            const date = document.getElementById('booking_date').value;\n
            const duration = document.getElementById('duration_hours').value;\n
\n
            if (!fieldId || !date || !duration) {\n
                return;\n
            }\n
\n
            // Load available time slots\n
            fetch(`http://localhost/reservations/check-availability`, {\n
                    method: 'POST',\n
                    headers: {\n
                        'Content-Type': 'application/json',\n
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')\n
                    },\n
                    body: JSON.stringify({\n
                        field_id: fieldId,\n
                        date: date,\n
                        duration_hours: parseInt(duration)\n
                    })\n
                })\n
                .then(response =&gt; response.json())\n
                .then(data =&gt; {\n
                    updateTimeSlots(data.slots || []);\n
                })\n
                .catch(error =&gt; {\n
                    console.error('Error loading availability:', error);\n
                });\n
        }\n
\n
        function updateTimeSlots(slots) {\n
            const timeSelect = document.getElementById('start_time');\n
            const currentValue = timeSelect.value || '14:00';\n
\n
            // Clear existing options\n
            timeSelect.innerHTML = '&lt;option value=""&gt;Select Time&lt;/option&gt;';\n
\n
            // Add available slots\n
            slots.forEach(slot =&gt; {\n
                const option = document.createElement('option');\n
                option.value = slot.start_time;\n
                option.textContent = slot.display;\n
                if (slot.start_time === currentValue) {\n
                    option.selected = true;\n
                }\n
                timeSelect.appendChild(option);\n
            });\n
        }\n
\n
        // Initialize on page load\n
        document.addEventListener('DOMContentLoaded', function() {\n
            updateFieldInfo();\n
            calculateCost();\n
            loadAvailability();\n
        });\n
    &lt;/script&gt;\n
\n
&lt;/body&gt;\n
\n
&lt;/html&gt;\n
' [UTF-8](length: 48063) contains "value="1" selected" [ASCII](length: 18).

/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Testing/TestResponseAssert.php:45
/Users/<USER>/Projects/Clients/FPMP/smp_online/vendor/laravel/framework/src/Illuminate/Testing/TestResponse.php:686
/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php:156</failure>
    </testcase>
    <testcase name="calendar_modal_handles_working_hours_validation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="162" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="5" time="0.004579">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Feature\CalendarModalReservationTest::calendar_modal_handles_working_hours_validation
Failed asserting that 'R' [ASCII](length: 1) contains "working hours" [ASCII](length: 13).

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php:179</failure>
    </testcase>
    <testcase name="calendar_modal_handles_duration_validation" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php" line="183" class="Tests\Feature\CalendarModalReservationTest" classname="Tests.Feature.CalendarModalReservationTest" assertions="5" time="0.004308">
      <failure type="PHPUnit\Framework\ExpectationFailedException">Tests\Feature\CalendarModalReservationTest::calendar_modal_handles_duration_validation
Failed asserting that 'The duration hours field must not be greater than 8.' [ASCII](length: 52) contains "Duration must be between" [ASCII](length: 24).

/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/CalendarModalReservationTest.php:200</failure>
    </testcase>
  </testsuite>
</testsuites>
