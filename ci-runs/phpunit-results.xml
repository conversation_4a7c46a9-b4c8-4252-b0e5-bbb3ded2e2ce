<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="/Users/<USER>/Projects/Clients/FPMP/smp_online/phpunit.xml" tests="1" assertions="3" errors="0" failures="0" skipped="0" time="0.079284">
    <testsuite name="Feature" tests="1" assertions="3" errors="0" failures="0" skipped="0" time="0.079284">
      <testsuite name="Tests\Feature\AmenityCrudTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" tests="1" assertions="3" errors="0" failures="0" skipped="0" time="0.079284">
        <testcase name="admin_can_update_amenity_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/AmenityCrudTest.php" line="158" class="Tests\Feature\AmenityCrudTest" classname="Tests.Feature.AmenityCrudTest" assertions="3" time="0.079284">
          <system-out>✅ Admin can update amenity successfully
</system-out>
        </testcase>
      </testsuite>
    </testsuite>
  </testsuite>
</testsuites>
