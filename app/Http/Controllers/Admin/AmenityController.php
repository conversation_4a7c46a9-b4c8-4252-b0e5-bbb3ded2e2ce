<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Amenity;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AmenityController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View|JsonResponse
    {
        if ($request->ajax()) {
            $amenities = Amenity::withCount('fields')
                ->when($request->search, function ($query, $search) {
                    return $query->where('name', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%");
                })
                ->when($request->status !== null, function ($query) use ($request) {
                    return $query->where('is_active', $request->status);
                })
                ->orderBy($request->get('sort', 'name'), $request->get('direction', 'asc'))
                ->paginate($request->get('per_page', 10));

            return response()->json($amenities);
        }

        return view('admin.amenities.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('admin.amenities.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:amenities,name',
            'description' => 'nullable|string|max:1000',
            'icon_class' => 'required|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Handle boolean value properly - use the validated value or default to false
        $validated['is_active'] = $validated['is_active'] ?? false;

        $amenity = Amenity::create($validated);

        return redirect()
            ->route('admin.amenities.show', $amenity)
            ->with('success', 'Amenity created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Amenity $amenity): View
    {
        $amenity->load('fields');

        return view('admin.amenities.show', compact('amenity'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Amenity $amenity): View
    {
        return view('admin.amenities.edit', compact('amenity'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Amenity $amenity): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:amenities,name,'.$amenity->id,
            'description' => 'nullable|string|max:1000',
            'icon_class' => 'required|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Handle boolean value properly - use the validated value or default to false
        $validated['is_active'] = $validated['is_active'] ?? false;

        $amenity->update($validated);

        return redirect()
            ->route('admin.amenities.show', $amenity)
            ->with('success', 'Amenity updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Amenity $amenity): RedirectResponse
    {
        // Check if amenity is being used by any fields
        if ($amenity->fields()->count() > 0) {
            return redirect()
                ->route('admin.amenities.index')
                ->with('error', 'Cannot delete amenity that is being used by fields. Please remove it from all fields first.');
        }

        $amenity->delete();

        return redirect()
            ->route('admin.amenities.index')
            ->with('success', 'Amenity deleted successfully.');
    }

    /**
     * Bulk actions for amenities.
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'amenities' => 'required|array',
            'amenities.*' => 'exists:amenities,id',
        ]);

        $amenities = Amenity::whereIn('id', $request->amenities);

        switch ($request->action) {
            case 'activate':
                $amenities->update(['is_active' => true]);
                $message = 'Selected amenities have been activated.';
                break;
            case 'deactivate':
                $amenities->update(['is_active' => false]);
                $message = 'Selected amenities have been deactivated.';
                break;
            case 'delete':
                // Check if any of the amenities are being used
                $usedAmenities = $amenities->withCount('fields')->get()->filter(function ($amenity) {
                    return $amenity->fields_count > 0;
                });

                if ($usedAmenities->count() > 0) {
                    return redirect()
                        ->route('admin.amenities.index')
                        ->with('error', 'Cannot delete amenities that are being used by fields.');
                }

                $amenities->delete();
                $message = 'Selected amenities have been deleted.';
                break;
        }

        return redirect()
            ->route('admin.amenities.index')
            ->with('success', $message);
    }
}
